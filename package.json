{"name": "ems_manage_local", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"@vicons/ionicons5": "^0.13.0", "alova": "^3.3.4", "naive-ui": "^2.42.0", "normalize.css": "^8.0.1", "pinia": "^3.0.3", "pinia-plugin-persistedstate": "^4.5.0", "vue": "^3.5.18", "vue-router": "4"}, "devDependencies": {"@types/node": "^24.3.0", "@vitejs/plugin-vue": "^6.0.1", "@vue/tsconfig": "^0.7.0", "sass-embedded": "^1.91.0", "typescript": "~5.8.3", "unocss": "^66.4.2", "unplugin-auto-import": "^20.1.0", "unplugin-vue-components": "^29.0.0", "vite": "^7.1.2", "vue-tsc": "^3.0.5"}}