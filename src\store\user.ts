// import { defineStore, storeToRefs } from 'pinia'
// import { ref } from 'vue'
// import { login, editPwd, resetPwd, logout } from '@/api/user'
// import { routes } from '@/router'
// import { useGlobalStore } from '../global'

// export const useUserStore = defineStore(
//   'user',
//   () => {
//     const { snackbar, snackbarText } = storeToRefs(useGlobalStore())
//     const isLogin = ref(false)
//     const userInfo = ref({})
//     const menuInfo = ref([])
//     const loginFn = async (data) => {
//       menuInfo.value = []
//       const res = await login(data)
//       if (res.code !== 200) {
//         snackbar.value = true
//         snackbarText.value = res.msg
//         return new Error(res.msg)
//       }
//       isLogin.value = true
//       userInfo.value = {
//         ...res.data,
//         password: data.password
//       }
//       // 根据权限分配菜单
//       const mapRoutes = (list) => {
//         list.map((item) => {
//           if (item.children && item.path == '/') {
//             mapRoutes(item.children)
//           } else {
//             if (
//               item.meta?.roles?.findIndex(
//                 (item) => item == res.data.permission_level
//               ) !== -1 &&
//               !item.children
//             ) {
//               menuInfo.value.push({
//                 text: item.meta.title,
//                 icon: item.meta.icon,
//                 path: item.path,
//                 sort: item.meta.sort,
//                 isHidden: item.meta.isHidden
//               })
//             } else {
//               if (!item?.children) return
//               let children = []
//               item?.children.forEach((item1) => {
//                 children.push({
//                   text: item1.meta.title,
//                   icon: item1.meta.icon,
//                   path: `${item.path}/${item1.path}`,
//                   sort: item1.meta.sort,
//                   isHidden: item.meta.isHidden
//                 })
//               })
//               if (
//                 item.meta?.roles?.findIndex(
//                   (item) => item == res.data.permission_level
//                 ) !== -1
//               ) {
//                 menuInfo.value.push({
//                   text: item.meta.title,
//                   icon: item.meta.icon,
//                   path: item.path,
//                   sort: item.meta.sort,
//                   children,
//                   isHidden: item.meta.isHidden
//                 })
//               }
//             }
//           }
//         })
//       }

//       mapRoutes(routes)
//       menuInfo.value.sort((a, b) => {
//         return a.sort - b.sort
//       })
//     }

//     // 退出登录
//     const loginOut = async () => {
//       await logout()
//       isLogin.value = false
//     }

//     // 修改密码
//     const editPwdFn = async (data) => {
//       const res = await editPwd(data)
//       if (res.code !== 200) {
//         snackbar.value = true
//         snackbarText.value = res.msg
//         return new Error(res.msg)
//       } else return res.data
//     }

//     // 重置密码
//     const resetPwdFn = async (data) => {
//       const res = await resetPwd(data)
//       if (res.code !== 200) {
//         snackbar.value = true
//         snackbarText.value = res.msg
//         return new Error(res.msg)
//       }
//       return res.data
//     }

//     return {
//       userInfo,
//       menuInfo,
//       loginFn,
//       isLogin,
//       loginOut,
//       editPwdFn,
//       resetPwdFn
//     }
//   },
//   {
//     persist: [
//       {
//         pick: ['userInfo', 'menuInfo'],
//         storage: localStorage
//       },
//       {
//         pick: ['isLogin'],
//         storage: sessionStorage
//       }
//     ]
//   }
// )
