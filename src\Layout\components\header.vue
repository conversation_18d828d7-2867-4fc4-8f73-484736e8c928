<!--
 * @Author: gg-guang <EMAIL>
 * @Date: 2025-08-29 12:22:58
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2025-08-29 12:23:20
 * @FilePath: \ems_manage_local\src\Layout\components\header.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<script setup lang="ts">

</script>

<template>
  <div class="flex items-center header">
    <div class="logo"></div>
    <div class="header-time"></div>
  </div>
</template>

<style scoped>

</style>