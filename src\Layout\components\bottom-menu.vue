<!--
 * @Author: gg-guang <EMAIL>
 * @Date: 2025-08-29 14:24:23
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2025-08-29 15:40:44
 * @FilePath: \ems_manage_local\src\Layout\components\bottom-menu.vue
 * @Description: EMS管理系统底部菜单组件
-->
<script setup lang="ts">
import { ref } from 'vue'
import {
  BarChartOutline,
  SettingsOutline,
  DocumentTextOutline,
  CogOutline,
  TrendingUpOutline,
  PersonOutline,
  InformationCircleOutline,
  TerminalOutline,
  ImageOutline,
  StatsChartOutline,
  HardwareChipOutline,
  BatteryChargingOutline,
  EllipsisHorizontalOutline
} from '@vicons/ionicons5'
import { NIcon } from 'naive-ui'

const showMoreMenu = ref(false)
const activeTab = ref('overview')

// 更多菜单项
const moreMenuItems = [
  { key: 'data-analysis', label: '数据分析', icon: BarChartOutline },
  { key: 'strategy', label: '运行策略', icon: SettingsOutline },
  { key: 'log-management', label: '日志管理', icon: DocumentTextOutline },
  { key: 'config-center', label: '配置中心', icon: CogOutline },
  { key: 'system-revenue', label: '系统收益', icon: TrendingUpOutline },
  { key: 'user-management', label: '用户管理', icon: PersonOutline },
  { key: 'system-info', label: '系统信息', icon: InformationCircleOutline },
  { key: 'backend-logs', label: '后台日志', icon: TerminalOutline },
  { key: 'edit-chart', label: '编辑图', icon: ImageOutline }
]

// 底部主导航项
const mainTabs = [
  { key: 'overview', label: '数据概览', icon: StatsChartOutline },
  { key: 'device', label: '设备详情', icon: HardwareChipOutline },
  { key: 'power', label: '电量统计', icon: BatteryChargingOutline },
  { key: 'more', label: '更多', icon: EllipsisHorizontalOutline }
]

const handleTabClick = (key: string) => {
  if (key === 'more') {
    showMoreMenu.value = !showMoreMenu.value
  } else {
    activeTab.value = key
    showMoreMenu.value = false
    // 这里可以添加路由跳转逻辑
    // router.push(`/${key}`)
  }
}

const handleMoreItemClick = (key: string) => {
  showMoreMenu.value = false
  // 这里可以添加路由跳转逻辑
  // router.push(`/${key}`)
  console.log('点击了:', key)
}
</script>

<template>
  <div class="bottom-menu-container">
    <!-- 更多菜单弹出层 -->
    <div
      v-if="showMoreMenu"
      class="more-menu-overlay"
      @click="showMoreMenu = false"
    >
      <div class="more-menu" @click.stop>
        <div class="more-menu-grid">
          <div
            v-for="item in moreMenuItems"
            :key="item.key"
            class="more-menu-item"
            @click="handleMoreItemClick(item.key)"
          >
            <div class="menu-item-icon">
              <n-icon :component="item.icon" size="24" />
            </div>
            <div class="menu-item-label">{{ item.label }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部导航栏 -->
    <div class="bottom-nav">
      <div
        v-for="tab in mainTabs"
        :key="tab.key"
        class="nav-item"
        :class="{ active: activeTab === tab.key }"
        @click="handleTabClick(tab.key)"
      >
        <div class="nav-icon">
          <n-icon :component="tab.icon" size="20" />
        </div>
        <div class="nav-label">{{ tab.label }}</div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.bottom-menu-container {
  position: relative;
  width: 100%;
}

.more-menu-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  z-index: 1000;
  display: flex;
  align-items: flex-end;
  justify-content: flex-end;
}

.more-menu {
  background: #1890ff;
  border-radius: 8px 8px 0 0;
  padding: 20px;
  margin-right: 20px;
  margin-bottom: 60px;
  box-shadow: 0 -4px 12px rgba(0, 0, 0, 0.15);
  min-width: 200px;
}

.more-menu-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 12px;
}

.more-menu-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.2s;
  color: white;
}

.more-menu-item:hover {
  background: rgba(255, 255, 255, 0.1);
}

.menu-item-icon {
  margin-right: 12px;
  display: flex;
  align-items: center;
}

.menu-item-label {
  font-size: 14px;
  font-weight: 500;
}

.bottom-nav {
  display: flex;
  background: #1890ff;
  height: 60px;
  align-items: center;
  justify-content: space-around;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 1;
  height: 100%;
  cursor: pointer;
  transition: all 0.2s;
  color: rgba(255, 255, 255, 0.7);
  position: relative;
}

.nav-item:hover,
.nav-item.active {
  color: white;
  background: rgba(255, 255, 255, 0.1);
}

.nav-icon {
  margin-bottom: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nav-label {
  font-size: 12px;
  font-weight: 500;
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .more-menu {
    margin-right: 10px;
    margin-bottom: 70px;
    min-width: 180px;
  }

  .nav-label {
    font-size: 11px;
  }

  .more-menu-item {
    padding: 10px 12px;
  }

  .menu-item-label {
    font-size: 13px;
  }
}
</style>
