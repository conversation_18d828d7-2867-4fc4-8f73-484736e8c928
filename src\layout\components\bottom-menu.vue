<script setup lang="ts">
import { h, ref } from 'vue'
import {
  LogOutOutline as HomeIcon,
  LaptopOutline as WorkIcon
} from '@vicons/ionicons5'

import { NIcon } from 'naive-ui'
import { RouterLink } from 'vue-router'
import MenuItem from './menu-item.vue'

import type { MenuOption, MenuGroupOption } from 'naive-ui'
import type { Component } from 'vue'

const activeKey = ref()

const renderIcon = (icon: Component) => {
  return h(() => h(NIcon, { size: '20px' }, { default: () => h(icon) }))
}

const renderLabel = (option) => {
  return h(
    'div',
    {
      style: {
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        textAlign: 'center',
        fontSize: '12px' // 文字小一点
      }
    },
    [
      // 图标（使用 render-icon 渲染）
      renderIcon(option.icon),
      // 文字
      h('div', null, option.label)
    ]
  )
}

const menuOptions: MenuOption[] = [
  {
    label: '数据概括',
    key: 'go-back-home',
    icon: HomeIcon,
    props: {
      height: '54px'
    }
  },
  {
    label: '实时数据',
    key: 'go-to-work',
    icon: WorkIcon
  }
]

const renderMenuItem = (option: MenuOption | MenuGroupOption) => {
  return {
    style: {
      height: '54px',
      padding: '0 15px',
      minWidth: '100px'
    }
  }
}
</script>

<template>
  <n-menu
    v-model:value="activeKey"
    :options="menuOptions"
    mode="horizontal"
    :node-props="renderMenuItem"
    :render-label="renderLabel"
    :render-icon="renderIcon"
    class="justify-center"
  />
</template>

<style lang="scss" scoped>
:deep(.n-menu-item-content__icon) {
  display: none !important;
}
</style>

<!-- <script setup lang="ts">
import { ref, onMounted } from 'vue'
import {
  HomeOutline,
  SearchOutline,
  AddCircleOutline,
  StarOutline,
  PersonOutline
} from '@vicons/ionicons5'

import { useMessage } from 'naive-ui'

const menuItems = ref([
  { key: '/home', label: '首页', icon: HomeOutline },
  { key: '/explore', label: '发现', icon: SearchOutline },
  { key: '/add', label: '添加', icon: AddCircleOutline },
  { key: '/favorites', label: '收藏', icon: StarOutline },
  { key: '/profile', label: '我的', icon: PersonOutline },
  { key: '/more', label: '更多', icon: PersonOutline, children: [
    {
      key: '/one', label: 'one', icon: HomeOutline
    }
  ] }
])

const currentPage = ref('home')
</script>

<template>
  <div class="w-full flex justify-center">
    <n-tabs class="bottom-nav" :bar-width="30">
      <n-tab-pane
        v-for="item in menuItems"
        :key="item.key"
        :name="item.key"
        :tab="item.label"
        :disabled="item.disabled"
      >
        <template #tab>
          <div class="nav-item" :class="{ active: currentPage === item.key }">
            <n-icon :component="item.icon" size="24" />
            <span class="nav-label">{{ item.label }}</span>
          </div>
        </template>
      </n-tab-pane>
    </n-tabs>
  </div>
</template>

<style scoped>
.bottom-nav {
  height: 60px;
  z-index: 1000;
  width: auto;
}

.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #999;
  font-size: 12px;
  transition: all 0.3s ease;
  padding: 0 10px;
  cursor: pointer;
}

.nav-label {
  margin-top: 4px;
}

.bottom-nav :deep(.n-tab-pane) {
  padding: 0;
}
</style> -->
