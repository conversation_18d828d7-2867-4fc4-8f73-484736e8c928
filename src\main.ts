/*
 * @Author: gg-guang <EMAIL>
 * @Date: 2025-08-28 16:00:07
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2025-08-29 10:36:35
 * @FilePath: \ems_manage_local\src\main.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { createApp } from 'vue'
import './style.css'
import 'normalize.css'
import App from './App.vue'
import router from '@/router'
import store from '@/store'
import 'virtual:uno.css'


const app = createApp(App)
app.use(router)
app.use(store)
app.mount('#app')
