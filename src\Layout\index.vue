<!--
 * @Author: gg-guang <EMAIL>
 * @Date: 2025-08-29 11:52:25
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2025-08-29 14:09:20
 * @FilePath: \ems_manage_local\src\Layout\index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<script setup lang="ts">
import Header from './components/header.vue'
import Main from './components/main.vue'
import BottomMenu from './components/bottom-menu.vue'
</script>

<template>
  <n-layout>
    <n-layout-header>
      <header />
    </n-layout-header>
    <n-layout-content content-style="padding: 20px;">
      <main />
    </n-layout-content>
    <n-layout-footer>
      <bottom-menu />
    </n-layout-footer>
  </n-layout>
</template>

<style scoped></style>
