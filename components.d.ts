/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    HelloWorld: typeof import('./src/components/HelloWorld.vue')['default']
    NButton: typeof import('naive-ui')['NButton']
    NIcon: typeof import('naive-ui')['NIcon']
    NLayout: typeof import('naive-ui')['NLayout']
    NLayoutContent: typeof import('naive-ui')['NLayoutContent']
    NLayoutFooter: typeof import('naive-ui')['NLayoutFooter']
    NLayoutHeader: typeof import('naive-ui')['NLayoutHeader']
    NMenu: typeof import('naive-ui')['NMenu']
    NTabBar: typeof import('naive-ui')['NTabBar']
    NTabPane: typeof import('naive-ui')['NTabPane']
    NTabs: typeof import('naive-ui')['NTabs']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
  }
}
