/*
 * @Author: gg-guang <EMAIL>
 * @Date: 2025-08-28 17:27:28
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2025-09-01 14:15:47
 * @FilePath: \ems_manage_local\src\router\index.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { createMemoryHistory, createRouter } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'
import { staticRoutes } from './staticRoutes'
// import { useUserStore } from '@/store/user'

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    redirect: 'Dashboard',
    component: () => import('@/layout/index.vue'),
    children: [
      {
        path: '/dashboard',
        name: 'Dashboard',
        component: () => import('@/views/Dashboard/index.vue'),
        meta: {
          roles: [4, 3, 2, 1],
          title: '数据概括',
          icon: 'mdi-home',
          sort: 1
        }
      },
      {
        path: '/device',
        name: 'Device',
        component: () => import('@/views/Device/index.vue'),
        meta: {
          roles: [4, 3, 2, 1],
          title: '实时数据',
          icon: 'mdi-monitor',
          sort: 2
        }
      },
      {
        path: '/analyse',
        name: 'Analyse',
        component: () => import('@/views/Analyse/index.vue'),
        meta: {
          roles: [4, 3, 2, 1],
          title: '数据分析',
          icon: 'mdi-chart-line',
          sort: 4
        }
      },
      {
        path: '/electric',
        name: 'Electric',
        component: () => import('@/views/Electric/index.vue'),
        meta: {
          roles: [4, 3, 2, 1],
          title: '电量统计',
          icon: 'mdi-chart-bar',
          sort: 3
        }
      },
      {
        path: '/strategy',
        name: 'OperationStrategy',
        component: () => import('@/views/OperationStrategy/index.vue'),
        meta: {
          roles: [4, 3, 2],
          title: '运行策略',
          icon: 'mdi-strategy',
          sort: 5
        }
      },
      {
        path: '/log',
        name: 'Log',
        component: () => import('@/views/Log/index.vue'),
        meta: {
          roles: [4, 3, 2],
          title: '日志管理',
          icon: 'mdi-book',
          sort: 6
        }
      },
      {
        path: '/config',
        name: 'Config',
        component: () => import('@/views/Config/index.vue'),
        meta: {
          roles: [4, 3],
          title: '配置中心',
          icon: 'mdi-cogs',
          sort: 6
        }
      }
    ]
  },
  ...staticRoutes
]

const router = createRouter({
  history: createMemoryHistory(),
  routes
})

router.beforeEach((to, from) => {
  // const user = useUserStore()
  if (!user.isLogin && to.path !== '/login') {
    return '/login'
  } else {
    if (staticRoutes.findIndex((item) => item.path == to.path) !== -1) {
      return
    } else if (
      to.meta?.roles?.findIndex(
        (item) => item == user.userInfo.permission_level
      ) !== -1
    ) {
      if (to.path == '/login') {
        // user.loginOut()
        // useConfigStore().stopLocalTimeUpdate()
      }
      return
    } else {
      return '/403'
    }
  }
})

export default router
