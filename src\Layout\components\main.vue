<!--
 * @Author: gg-guang <EMAIL>
 * @Date: 2025-08-29 14:12:33
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2025-08-29 14:12:53
 * @FilePath: \ems_manage_local\src\Layout\components\main.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<script setup lang="ts">

</script>

<template>
  <router-view>
    <template #default="{ Component, route }">
      <keep-alive>
        <component :is="Component" :key="route.path" />
      </keep-alive>
    </template>
  </router-view>
</template>

<style scoped>

</style>