/*
 * @Author: gg-guang <EMAIL>
 * @Date: 2025-08-29 10:49:24
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2025-08-29 11:49:03
 * @FilePath: \ems_manage_local\src\api\index.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { createAlova } from 'alova';
import adapterFetch from 'alova/fetch';
import VueHook from 'alova/vue'

const alovaInstance = createAlova({
  baseURL: '/api',
  requestAdapter: adapterFetch(),
  statesHook: VueHook,
  timeout: 50000,
  beforeRequest(method) {
    let lang = localStorage.getItem('lang') || 'en'
    method.config.headers['language'] = lang == 'zhHans' ? 'zh' : lang
    return method
  },
  responded: {
    // 请求成功的拦截器
    // 当使用 `alova/fetch` 请求适配器时，第一个参数接收Response对象
    // 第二个参数为当前请求的method实例，你可以用它同步请求前后的配置信息
    onSuccess: async (response, method) => {
      if (response.status >= 400) {
        throw new Error(response.statusText);
      }
      const json = await response.json();
      console.log(json)
      if (json.code !== 200) {
        // 抛出错误或返回reject状态的Promise实例时，此请求将抛出错误
        throw new Error(json.message);
      } else if (json.code == 401) {
        //  useUserStore().isLogin = false
        // useGlobalStore().showPanel = false
        // useConfigStore().logData = []
        // useConfigStore().logWs?.close()
        // useGlobalStore().snackbar = true
        // useGlobalStore().snackbarText = res.data.msg
        // router.push('/login')
        throw new Error(json.msg);
      }

      // 解析的响应数据将传给method实例的transform钩子函数，这些函数将在后续讲解
      return json.data;
    },

    // 请求失败的拦截器
    // 请求错误时将会进入该拦截器。
    // 第二个参数为当前请求的method实例，你可以用它同步请求前后的配置信息
    onError: (err, method) => {
      alert(err.message);
    },
  }
});