<!--
 * @Author: gg-guang <EMAIL>
 * @Date: 2025-08-29 11:52:25
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2025-08-29 16:52:51
 * @FilePath: \ems_manage_local\src\Layout\index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<script setup lang="ts">
import Navbar from './components/navbar.vue'
import MainContent from './components/main.vue'
import BottomMenu from './components/bottom-menu.vue'
</script>

<template>
  <n-layout position="absolute" content-class="flex flex-col">
    <n-layout-header>
      <navbar />
    </n-layout-header>
    <n-layout-content content-style="padding: 20px;" class="flex-1">
      <main-content />
    </n-layout-content>
    <n-layout-footer>
      <bottom-menu />
    </n-layout-footer>
  </n-layout>
</template>

<style scoped>
</style>
